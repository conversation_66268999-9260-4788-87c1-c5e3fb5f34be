#include "User.h"
uint8_t led_case = 0;

int main(void)
{
    GPIOA_Init();  // 初始化GPIOA
    
    LED_OFF(0);
    LED_OFF(1);
    LED_OFF(3);
    while(1)
    {
        switch (led_case)
        {
        case 1:
            LED_ON(0);
            LED_OFF(1);
            LED_OFF(3);
        break;

        case 2:
            LED_ON(1);
            LED_OFF(0);
            LED_OFF(3);
        break;

        case 3:
            LED_ON(3);
            LED_OFF(1);
            LED_OFF(0);
        break;
        
        default:
            LED_OFF(0);
            LED_OFF(1);
            LED_OFF(3);
            break;
        }
    }
}

