
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:207 (message)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      The target system is: Generic -  - arm
      The host system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeNinjaFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Program used to build from build.ninja files."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ninja-build"
      - "ninja"
      - "samu"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Users/<USER>/bin/ninja-build.com"
      - "C:/Users/<USER>/bin/ninja-build.exe"
      - "C:/Users/<USER>/bin/ninja-build"
      - "C:/Users/<USER>/bin/ninja.com"
      - "C:/Users/<USER>/bin/ninja.exe"
      - "C:/Users/<USER>/bin/ninja"
      - "C:/Users/<USER>/bin/samu.com"
      - "C:/Users/<USER>/bin/samu.exe"
      - "C:/Users/<USER>/bin/samu"
      - "C:/Program Files/Git/mingw64/bin/ninja-build.com"
      - "C:/Program Files/Git/mingw64/bin/ninja-build.exe"
      - "C:/Program Files/Git/mingw64/bin/ninja-build"
      - "C:/Program Files/Git/mingw64/bin/ninja.com"
      - "C:/Program Files/Git/mingw64/bin/ninja.exe"
      - "C:/Program Files/Git/mingw64/bin/ninja"
      - "C:/Program Files/Git/mingw64/bin/samu.com"
      - "C:/Program Files/Git/mingw64/bin/samu.exe"
      - "C:/Program Files/Git/mingw64/bin/samu"
      - "C:/Program Files/Git/usr/local/bin/ninja-build.com"
      - "C:/Program Files/Git/usr/local/bin/ninja-build.exe"
      - "C:/Program Files/Git/usr/local/bin/ninja-build"
      - "C:/Program Files/Git/usr/local/bin/ninja.com"
      - "C:/Program Files/Git/usr/local/bin/ninja.exe"
      - "C:/Program Files/Git/usr/local/bin/ninja"
      - "C:/Program Files/Git/usr/local/bin/samu.com"
      - "C:/Program Files/Git/usr/local/bin/samu.exe"
      - "C:/Program Files/Git/usr/local/bin/samu"
      - "C:/Program Files/Git/usr/bin/ninja-build.com"
      - "C:/Program Files/Git/usr/bin/ninja-build.exe"
      - "C:/Program Files/Git/usr/bin/ninja-build"
      - "C:/Program Files/Git/usr/bin/ninja.com"
      - "C:/Program Files/Git/usr/bin/ninja.exe"
      - "C:/Program Files/Git/usr/bin/ninja"
      - "C:/Program Files/Git/usr/bin/samu.com"
      - "C:/Program Files/Git/usr/bin/samu.exe"
      - "C:/Program Files/Git/usr/bin/samu"
      - "D:/APP/Vmware/bin/ninja-build.com"
      - "D:/APP/Vmware/bin/ninja-build.exe"
      - "D:/APP/Vmware/bin/ninja-build"
      - "D:/APP/Vmware/bin/ninja.com"
      - "D:/APP/Vmware/bin/ninja.exe"
      - "D:/APP/Vmware/bin/ninja"
      - "D:/APP/Vmware/bin/samu.com"
      - "D:/APP/Vmware/bin/samu.exe"
      - "D:/APP/Vmware/bin/samu"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja-build"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/ninja"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/samu"
      - "C:/Windows/System32/ninja-build.com"
      - "C:/Windows/System32/ninja-build.exe"
      - "C:/Windows/System32/ninja-build"
      - "C:/Windows/System32/ninja.com"
      - "C:/Windows/System32/ninja.exe"
      - "C:/Windows/System32/ninja"
      - "C:/Windows/System32/samu.com"
      - "C:/Windows/System32/samu.exe"
      - "C:/Windows/System32/samu"
      - "C:/Windows/ninja-build.com"
      - "C:/Windows/ninja-build.exe"
      - "C:/Windows/ninja-build"
      - "C:/Windows/ninja.com"
      - "C:/Windows/ninja.exe"
      - "C:/Windows/ninja"
      - "C:/Windows/samu.com"
      - "C:/Windows/samu.exe"
      - "C:/Windows/samu"
      - "C:/Windows/System32/wbem/ninja-build.com"
      - "C:/Windows/System32/wbem/ninja-build.exe"
      - "C:/Windows/System32/wbem/ninja-build"
      - "C:/Windows/System32/wbem/ninja.com"
      - "C:/Windows/System32/wbem/ninja.exe"
      - "C:/Windows/System32/wbem/ninja"
      - "C:/Windows/System32/wbem/samu.com"
      - "C:/Windows/System32/wbem/samu.exe"
      - "C:/Windows/System32/wbem/samu"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja-build"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/ninja"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/samu"
      - "C:/Windows/System32/OpenSSH/ninja-build.com"
      - "C:/Windows/System32/OpenSSH/ninja-build.exe"
      - "C:/Windows/System32/OpenSSH/ninja-build"
      - "C:/Windows/System32/OpenSSH/ninja.com"
      - "C:/Windows/System32/OpenSSH/ninja.exe"
      - "C:/Windows/System32/OpenSSH/ninja"
      - "C:/Windows/System32/OpenSSH/samu.com"
      - "C:/Windows/System32/OpenSSH/samu.exe"
      - "C:/Windows/System32/OpenSSH/samu"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja-build"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/ninja"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/samu"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja-build"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/ninja"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/samu"
      - "D:/APP/Microsoft VS Code/bin/ninja-build.com"
      - "D:/APP/Microsoft VS Code/bin/ninja-build.exe"
      - "D:/APP/Microsoft VS Code/bin/ninja-build"
      - "D:/APP/Microsoft VS Code/bin/ninja.com"
      - "D:/APP/Microsoft VS Code/bin/ninja.exe"
      - "D:/APP/Microsoft VS Code/bin/ninja"
      - "D:/APP/Microsoft VS Code/bin/samu.com"
      - "D:/APP/Microsoft VS Code/bin/samu.exe"
      - "D:/APP/Microsoft VS Code/bin/samu"
      - "D:/APP/mingw64/bin/ninja-build.com"
      - "D:/APP/mingw64/bin/ninja-build.exe"
      - "D:/APP/mingw64/bin/ninja-build"
      - "D:/APP/mingw64/bin/ninja.com"
      - "D:/APP/mingw64/bin/ninja.exe"
      - "D:/APP/mingw64/bin/ninja"
      - "D:/APP/mingw64/bin/samu.com"
      - "D:/APP/mingw64/bin/samu.exe"
      - "D:/APP/mingw64/bin/samu"
      - "C:/Program Files/nodejs/ninja-build.com"
      - "C:/Program Files/nodejs/ninja-build.exe"
      - "C:/Program Files/nodejs/ninja-build"
      - "C:/Program Files/nodejs/ninja.com"
      - "C:/Program Files/nodejs/ninja.exe"
      - "C:/Program Files/nodejs/ninja"
      - "C:/Program Files/nodejs/samu.com"
      - "C:/Program Files/nodejs/samu.exe"
      - "C:/Program Files/nodejs/samu"
      - "D:/APP/MATLAB/bin/ninja-build.com"
      - "D:/APP/MATLAB/bin/ninja-build.exe"
      - "D:/APP/MATLAB/bin/ninja-build"
      - "D:/APP/MATLAB/bin/ninja.com"
      - "D:/APP/MATLAB/bin/ninja.exe"
      - "D:/APP/MATLAB/bin/ninja"
      - "D:/APP/MATLAB/bin/samu.com"
      - "D:/APP/MATLAB/bin/samu.exe"
      - "D:/APP/MATLAB/bin/samu"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/ninja"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/samu"
      - "C:/Program Files/Git/cmd/ninja-build.com"
      - "C:/Program Files/Git/cmd/ninja-build.exe"
      - "C:/Program Files/Git/cmd/ninja-build"
      - "C:/Program Files/Git/cmd/ninja.com"
      - "C:/Program Files/Git/cmd/ninja.exe"
      - "C:/Program Files/Git/cmd/ninja"
      - "C:/Program Files/Git/cmd/samu.com"
      - "C:/Program Files/Git/cmd/samu.exe"
      - "C:/Program Files/Git/cmd/samu"
      - "C:/Program Files/PowerShell/7/ninja-build.com"
      - "C:/Program Files/PowerShell/7/ninja-build.exe"
      - "C:/Program Files/PowerShell/7/ninja-build"
      - "C:/Program Files/PowerShell/7/ninja.com"
      - "C:/Program Files/PowerShell/7/ninja.exe"
      - "C:/Program Files/PowerShell/7/ninja"
      - "C:/Program Files/PowerShell/7/samu.com"
      - "C:/Program Files/PowerShell/7/samu.exe"
      - "C:/Program Files/PowerShell/7/samu"
      - "C:/Program Files/dotnet/ninja-build.com"
      - "C:/Program Files/dotnet/ninja-build.exe"
      - "C:/Program Files/dotnet/ninja-build"
      - "C:/Program Files/dotnet/ninja.com"
      - "C:/Program Files/dotnet/ninja.exe"
      - "C:/Program Files/dotnet/ninja"
      - "C:/Program Files/dotnet/samu.com"
      - "C:/Program Files/dotnet/samu.exe"
      - "C:/Program Files/dotnet/samu"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja-build"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/ninja"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/samu"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja-build"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/ninja"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/samu"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build.com"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build.exe"
      - "D:/APP/clion/CLion_2025_1/bin/ninja-build"
      - "D:/APP/clion/CLion_2025_1/bin/ninja.com"
      - "D:/APP/clion/CLion_2025_1/bin/ninja.exe"
      - "D:/APP/clion/CLion_2025_1/bin/ninja"
      - "D:/APP/clion/CLion_2025_1/bin/samu.com"
      - "D:/APP/clion/CLion_2025_1/bin/samu.exe"
      - "D:/APP/clion/CLion_2025_1/bin/samu"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja-build"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/ninja"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/samu"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja-build"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/ninja"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/samu"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja-build"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/ninja"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/samu"
      - "D:/APP/ninja/ninja-win/ninja-build.com"
      - "D:/APP/ninja/ninja-win/ninja-build.exe"
      - "D:/APP/ninja/ninja-win/ninja-build"
      - "D:/APP/ninja/ninja-win/ninja.com"
    found: "D:/APP/ninja/ninja-win/ninja.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:115 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:67 (_cmake_find_compiler_path)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc"
      - "C:/Windows/System32/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc"
      - "C:/Windows/arm-none-eabi-gcc.com"
      - "C:/Windows/arm-none-eabi-gcc.exe"
      - "C:/Windows/arm-none-eabi-gcc"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x28): undefined reference to `_exit'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-closer.o): in function `_close_r':
      closer.c:(.text._close_r+0x18): undefined reference to `_close'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
      lseekr.c:(.text._lseek_r+0x24): undefined reference to `_lseek'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-readr.o): in function `_read_r':
      readr.c:(.text._read_r+0x24): undefined reference to `_read'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-writer.o): in function `_write_r':
      writer.c:(.text._write_r+0x24): undefined reference to `_write'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      sbrkr.c:(.text._sbrk_r+0x18): undefined reference to `_sbrk'
      collect2.exe: error: ld returned 1 exit status
      
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"
      
      The C compiler identification is GNU, found in:
        D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/4.1.0-rc1/CompilerIdC/CMakeCCompilerId.o
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:37 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:58 (__resolve_tool_path)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "_CMAKE_TOOL_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-g++"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-ar"
      - "ar"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-ar.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-ranlib"
      - "ranlib"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-ranlib.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-strip"
      - "strip"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-strip.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-strip.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-nm"
      - "nm"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-nm.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-nm.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-objdump"
      - "objdump"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-objdump.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-objdump.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-readelf"
      - "readelf"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-readelf.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-readelf.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-dlltool"
      - "dlltool"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-dlltool"
      - "C:/Users/<USER>/bin/arm-none-eabi-dlltool.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-dlltool.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-dlltool"
      - "D:/APP/Vmware/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-dlltool"
      - "C:/Windows/System32/arm-none-eabi-dlltool.com"
      - "C:/Windows/System32/arm-none-eabi-dlltool.exe"
      - "C:/Windows/System32/arm-none-eabi-dlltool"
      - "C:/Windows/arm-none-eabi-dlltool.com"
      - "C:/Windows/arm-none-eabi-dlltool.exe"
      - "C:/Windows/arm-none-eabi-dlltool"
      - "C:/Windows/System32/wbem/arm-none-eabi-dlltool.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-dlltool.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-dlltool"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-dlltool.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-dlltool.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-dlltool"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-dlltool.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-dlltool.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-dlltool"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-dlltool.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-dlltool.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-dlltool"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-dlltool.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-dlltool"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-dlltool"
      - "D:/APP/mingw64/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/nodejs/arm-none-eabi-dlltool.com"
      - "C:/Program Files/nodejs/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-dlltool"
      - "D:/APP/MATLAB/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-dlltool"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/cmd/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-dlltool"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-dlltool.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-dlltool"
      - "C:/Program Files/dotnet/arm-none-eabi-dlltool.com"
      - "C:/Program Files/dotnet/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-dlltool"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-dlltool.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-dlltool.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-dlltool"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-dlltool.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-dlltool.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-dlltool"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-dlltool"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-dlltool"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-dlltool.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-dlltool.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-dlltool"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-dlltool.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-dlltool.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-dlltool"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-dlltool.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-dlltool.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-dlltool"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/dlltool.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/dlltool.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/dlltool"
      - "C:/Users/<USER>/bin/dlltool.com"
      - "C:/Users/<USER>/bin/dlltool.exe"
      - "C:/Users/<USER>/bin/dlltool"
      - "C:/Program Files/Git/mingw64/bin/dlltool.com"
      - "C:/Program Files/Git/mingw64/bin/dlltool.exe"
      - "C:/Program Files/Git/mingw64/bin/dlltool"
      - "C:/Program Files/Git/usr/local/bin/dlltool.com"
      - "C:/Program Files/Git/usr/local/bin/dlltool.exe"
      - "C:/Program Files/Git/usr/local/bin/dlltool"
      - "C:/Program Files/Git/usr/bin/dlltool.com"
      - "C:/Program Files/Git/usr/bin/dlltool.exe"
      - "C:/Program Files/Git/usr/bin/dlltool"
      - "D:/APP/Vmware/bin/dlltool.com"
      - "D:/APP/Vmware/bin/dlltool.exe"
      - "D:/APP/Vmware/bin/dlltool"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/dlltool.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/dlltool.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/dlltool"
      - "C:/Windows/System32/dlltool.com"
      - "C:/Windows/System32/dlltool.exe"
      - "C:/Windows/System32/dlltool"
      - "C:/Windows/dlltool.com"
      - "C:/Windows/dlltool.exe"
      - "C:/Windows/dlltool"
      - "C:/Windows/System32/wbem/dlltool.com"
      - "C:/Windows/System32/wbem/dlltool.exe"
      - "C:/Windows/System32/wbem/dlltool"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/dlltool.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/dlltool.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/dlltool"
      - "C:/Windows/System32/OpenSSH/dlltool.com"
      - "C:/Windows/System32/OpenSSH/dlltool.exe"
      - "C:/Windows/System32/OpenSSH/dlltool"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/dlltool.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/dlltool.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/dlltool"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/dlltool.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/dlltool.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/dlltool"
      - "D:/APP/Microsoft VS Code/bin/dlltool.com"
      - "D:/APP/Microsoft VS Code/bin/dlltool.exe"
      - "D:/APP/Microsoft VS Code/bin/dlltool"
      - "D:/APP/mingw64/bin/dlltool.com"
    found: "D:/APP/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-addr2line"
      - "addr2line"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-addr2line.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-addr2line.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-tapi"
      - "tapi"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-tapi.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-tapi"
      - "C:/Users/<USER>/bin/arm-none-eabi-tapi.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-tapi.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-tapi"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-tapi"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-tapi"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-tapi"
      - "D:/APP/Vmware/bin/arm-none-eabi-tapi.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-tapi"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-tapi.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-tapi"
      - "C:/Windows/System32/arm-none-eabi-tapi.com"
      - "C:/Windows/System32/arm-none-eabi-tapi.exe"
      - "C:/Windows/System32/arm-none-eabi-tapi"
      - "C:/Windows/arm-none-eabi-tapi.com"
      - "C:/Windows/arm-none-eabi-tapi.exe"
      - "C:/Windows/arm-none-eabi-tapi"
      - "C:/Windows/System32/wbem/arm-none-eabi-tapi.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-tapi.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-tapi"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-tapi.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-tapi.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-tapi"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-tapi.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-tapi"
      - "D:/APP/mingw64/bin/arm-none-eabi-tapi.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-tapi"
      - "C:/Program Files/nodejs/arm-none-eabi-tapi.com"
      - "C:/Program Files/nodejs/arm-none-eabi-tapi.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-tapi"
      - "D:/APP/MATLAB/bin/arm-none-eabi-tapi.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-tapi"
      - "C:/Program Files/Git/cmd/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-tapi"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-tapi.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-tapi.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-tapi"
      - "C:/Program Files/dotnet/arm-none-eabi-tapi.com"
      - "C:/Program Files/dotnet/arm-none-eabi-tapi.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-tapi"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-tapi.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-tapi"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-tapi.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-tapi"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-tapi.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-tapi.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-tapi"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-tapi.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-tapi.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-tapi"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-tapi"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-tapi.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-tapi.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-tapi"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/tapi"
      - "C:/Users/<USER>/bin/tapi.com"
      - "C:/Users/<USER>/bin/tapi.exe"
      - "C:/Users/<USER>/bin/tapi"
      - "C:/Program Files/Git/mingw64/bin/tapi.com"
      - "C:/Program Files/Git/mingw64/bin/tapi.exe"
      - "C:/Program Files/Git/mingw64/bin/tapi"
      - "C:/Program Files/Git/usr/local/bin/tapi.com"
      - "C:/Program Files/Git/usr/local/bin/tapi.exe"
      - "C:/Program Files/Git/usr/local/bin/tapi"
      - "C:/Program Files/Git/usr/bin/tapi.com"
      - "C:/Program Files/Git/usr/bin/tapi.exe"
      - "C:/Program Files/Git/usr/bin/tapi"
      - "D:/APP/Vmware/bin/tapi.com"
      - "D:/APP/Vmware/bin/tapi.exe"
      - "D:/APP/Vmware/bin/tapi"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/tapi"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/tapi"
      - "D:/APP/Microsoft VS Code/bin/tapi.com"
      - "D:/APP/Microsoft VS Code/bin/tapi.exe"
      - "D:/APP/Microsoft VS Code/bin/tapi"
      - "D:/APP/mingw64/bin/tapi.com"
      - "D:/APP/mingw64/bin/tapi.exe"
      - "D:/APP/mingw64/bin/tapi"
      - "C:/Program Files/nodejs/tapi.com"
      - "C:/Program Files/nodejs/tapi.exe"
      - "C:/Program Files/nodejs/tapi"
      - "D:/APP/MATLAB/bin/tapi.com"
      - "D:/APP/MATLAB/bin/tapi.exe"
      - "D:/APP/MATLAB/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/tapi"
      - "C:/Program Files/Git/cmd/tapi.com"
      - "C:/Program Files/Git/cmd/tapi.exe"
      - "C:/Program Files/Git/cmd/tapi"
      - "C:/Program Files/PowerShell/7/tapi.com"
      - "C:/Program Files/PowerShell/7/tapi.exe"
      - "C:/Program Files/PowerShell/7/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/tapi"
      - "D:/APP/clion/CLion_2025_1/bin/tapi.com"
      - "D:/APP/clion/CLion_2025_1/bin/tapi.exe"
      - "D:/APP/clion/CLion_2025_1/bin/tapi"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/tapi"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/tapi"
      - "D:/APP/ninja/ninja-win/tapi.com"
      - "D:/APP/ninja/ninja-win/tapi.exe"
      - "D:/APP/ninja/ninja-win/tapi"
      - "C:/Program Files/Git/usr/bin/vendor_perl/tapi.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/tapi.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/tapi"
      - "C:/Program Files/Git/usr/bin/core_perl/tapi.com"
      - "C:/Program Files/Git/usr/bin/core_perl/tapi.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ar-14.2"
      - "arm-none-eabi-gcc-ar-14"
      - "arm-none-eabi-gcc-ar14"
      - "arm-none-eabi-gcc-ar"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14"
      - "C:/Windows/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ranlib-14.2"
      - "arm-none-eabi-gcc-ranlib-14"
      - "arm-none-eabi-gcc-ranlib14"
      - "arm-none-eabi-gcc-ranlib"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:115 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:64 (_cmake_find_compiler_path)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc"
      - "C:/Windows/System32/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc"
      - "C:/Windows/arm-none-eabi-gcc.com"
      - "C:/Windows/arm-none-eabi-gcc.exe"
      - "C:/Windows/arm-none-eabi-gcc"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:1290 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:170 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" matched "(GNU assembler)|(GCC)|(Free Software Foundation)":
      arm-none-eabi-gcc.exe (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 14.2.1 20241119
      Copyright (C) 2024 Free Software Foundation, Inc.
      This is free software; see the source for copying conditions.  There is NO
      warranty; not even for MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ar-"
      - "arm-none-eabi-gcc-ar-"
      - "arm-none-eabi-gcc-ar"
      - "arm-none-eabi-gcc-ar"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-"
      - "C:/Windows/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-"
      - "C:/Windows/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineASMCompiler.cmake:268 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_ASM_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ranlib-"
      - "arm-none-eabi-gcc-ranlib-"
      - "arm-none-eabi-gcc-ranlib"
      - "arm-none-eabi-gcc-ranlib"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake:115 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:71 (_cmake_find_compiler_path)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-g++"
    candidate_directories:
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "C:/Users/<USER>/bin/arm-none-eabi-g++.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-g++.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-g++"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-g++.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-g++.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-g++"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-g++.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-g++.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-g++"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-g++.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-g++.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-g++"
      - "D:/APP/Vmware/bin/arm-none-eabi-g++.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-g++.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-g++"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-g++.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-g++.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-g++"
      - "C:/Windows/System32/arm-none-eabi-g++.com"
      - "C:/Windows/System32/arm-none-eabi-g++.exe"
      - "C:/Windows/System32/arm-none-eabi-g++"
      - "C:/Windows/arm-none-eabi-g++.com"
      - "C:/Windows/arm-none-eabi-g++.exe"
      - "C:/Windows/arm-none-eabi-g++"
      - "C:/Windows/System32/wbem/arm-none-eabi-g++.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-g++.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-g++"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-g++.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-g++.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-g++"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-g++.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-g++.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-g++"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-g++.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-g++.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-g++"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-g++.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-g++.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-g++"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-g++.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-g++.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-g++"
      - "D:/APP/mingw64/bin/arm-none-eabi-g++.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-g++.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-g++"
      - "C:/Program Files/nodejs/arm-none-eabi-g++.com"
      - "C:/Program Files/nodejs/arm-none-eabi-g++.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-g++"
      - "D:/APP/MATLAB/bin/arm-none-eabi-g++.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-g++.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-g++"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-g++.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-g++.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-g++"
      - "C:/Program Files/Git/cmd/arm-none-eabi-g++.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-g++.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-g++"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-g++.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-g++.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-g++"
      - "C:/Program Files/dotnet/arm-none-eabi-g++.com"
      - "C:/Program Files/dotnet/arm-none-eabi-g++.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-g++"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-g++.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-g++.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-g++"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-g++.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-g++.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-g++"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-g++.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-g++.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-g++"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      1
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-exit.o): in function `exit':
      exit.c:(.text.exit+0x28): undefined reference to `_exit'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-closer.o): in function `_close_r':
      closer.c:(.text._close_r+0x18): undefined reference to `_close'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
      lseekr.c:(.text._lseek_r+0x24): undefined reference to `_lseek'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-readr.o): in function `_read_r':
      readr.c:(.text._read_r+0x24): undefined reference to `_read'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-writer.o): in function `_write_r':
      writer.c:(.text._write_r+0x24): undefined reference to `_write'
      D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
      sbrkr.c:(.text._sbrk_r+0x18): undefined reference to `_sbrk'
      collect2.exe: error: ld returned 1 exit status
      
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/"
    found: "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "message-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:28 (enable_language)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-g++.exe 
      Build flags: 
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"
      
      The CXX compiler identification is GNU, found in:
        D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/4.1.0-rc1/CompilerIdCXX/CMakeCXXCompilerId.o
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ar-14.2"
      - "arm-none-eabi-gcc-ar-14"
      - "arm-none-eabi-gcc-ar14"
      - "arm-none-eabi-gcc-ar"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.2"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.2"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.2"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar-14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar-14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar-14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar-14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar-14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar-14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar-14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ar14"
      - "C:/Windows/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ar14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ar14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ar14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ar14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ar14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ar14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ar14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ar14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ar14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "find-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:28 (enable_language)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "arm-none-eabi-gcc-ranlib-14.2"
      - "arm-none-eabi-gcc-ranlib-14"
      - "arm-none-eabi-gcc-ranlib14"
      - "arm-none-eabi-gcc-ranlib"
    candidate_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/"
      - "C:/Users/<USER>/bin/"
      - "C:/Program Files/Git/mingw64/bin/"
      - "C:/Program Files/Git/usr/local/bin/"
      - "C:/Program Files/Git/usr/bin/"
      - "D:/APP/Vmware/bin/"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "D:/APP/Microsoft VS Code/bin/"
      - "D:/APP/mingw64/bin/"
      - "C:/Program Files/nodejs/"
      - "D:/APP/MATLAB/bin/"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/"
      - "C:/Program Files/Git/cmd/"
      - "C:/Program Files/PowerShell/7/"
      - "C:/Program Files/dotnet/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "D:/APP/clion/CLion_2025_1/bin/"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/"
      - "D:/APP/ninja/ninja-win/"
      - "C:/Program Files/Git/usr/bin/vendor_perl/"
      - "C:/Program Files/Git/usr/bin/core_perl/"
    searched_directories:
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.2"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.2"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib-14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib-14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib-14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib-14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/mingw64/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/local/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/Vmware/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Common Files/Oracle/Java/javapath/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/wbem/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/arm-none-eabi-gcc-ranlib14"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Windows/System32/OpenSSH/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/Microsoft VS Code/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/mingw64/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/nodejs/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/MATLAB/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/bin/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/cmd/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/PowerShell/7/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/dotnet/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/arm-none-eabi-gcc-ranlib14"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Users/<USER>/AppData/Roaming/npm/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/clion/CLion_2025_1/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/openocd/xpack-openocd-0.12.0-5-win32-x64/xpack-openocd-0.12.0-5/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/bin/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14.com"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14.exe"
      - "D:/APP/ninja/ninja-win/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/vendor_perl/arm-none-eabi-gcc-ranlib14"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14.com"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14.exe"
      - "C:/Program Files/Git/usr/bin/core_perl/arm-none-eabi-gcc-ranlib14"
      - "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.com"
    found: "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/arm-none-eabi-gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "C:\\Users\\<USER>\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\local\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Program Files\\Git\\mingw64\\bin"
        - "C:\\Program Files\\Git\\usr\\bin"
        - "C:\\Users\\<USER>\\bin"
        - "D:\\APP\\Vmware\\bin"
        - "C:\\Program Files\\Common Files\\Oracle\\Java\\javapath"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "D:\\APP\\mingw64\\bin"
        - "C:\\Program Files\\nodejs"
        - "D:\\APP\\MATLAB\\bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\Program Files\\PowerShell\\7"
        - "C:\\Program Files\\dotnet"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "D:\\APP\\Microsoft VS Code\\bin"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin"
        - "D:\\APP\\clion\\CLion_2025_1\\bin"
        - "D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin"
        - "D:\\APP\\openocd\\xpack-openocd-0.12.0-5-win32-x64\\xpack-openocd-0.12.0-5\\bin"
        - "D:\\APP\\cmake\\cmake-4.1.0-rc1-windows-x86_64\\cmake-4.1.0-rc1-windows-x86_64\\bin"
        - "D:\\APP\\ninja\\ninja-win"
        - "C:\\Program Files\\Git\\usr\\bin\\vendor_perl"
        - "C:\\Program Files\\Git\\usr\\bin\\core_perl"
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (enable_language)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-kw6krl"
      binary: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-kw6krl"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-kw6krl'
        
        Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_7cae6
        [1/2] D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-gcc.exe   -std=gnu11   -v -o CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-gcc.exe
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --enable-mingw-wildcard --host=i686-w64-mingw32 --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_7cae6.dir/'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1.exe -quiet -v -iprefix D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles/cmTC_7cae6.dir/ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mcpu=arm7tdmi -mfloat-abi=soft -marm -mlibarch=armv4t -march=armv4t -std=gnu11 -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3rjbbr.s
        GNU C11 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)
        	compiled by GNU C version 9.3-win32 20200320, GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"
        ignoring nonexistent directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/lib/gcc/arm-none-eabi/14.2.1/../../../../include"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: 63ce4b303c7eb47a24bbf869f5c70b36
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_7cae6.dir/'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv4t -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3rjbbr.s
        GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119
        COMPILER_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-std=gnu11' '-v' '-o' 'CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.'
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-gcc.exe  -v CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj -o cmTC_7cae6   && cd ."
        FAILED: [code=1] cmTC_7cae6 
        C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-gcc.exe  -v CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj -o cmTC_7cae6   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-gcc.exe
        COLLECT_LTO_WRAPPER=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/lto-wrapper.exe
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --enable-mingw-wildcard --host=i686-w64-mingw32 --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COMPILER_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_7cae6' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'cmTC_7cae6.'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/collect2.exe -plugin D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/liblto_plugin.dll -plugin-opt=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccLIPYmw.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc --sysroot=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi -X -o cmTC_7cae6 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crti.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtbegin.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crt0.o -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1 -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib CMakeFiles/cmTC_7cae6.dir/CMakeCCompilerABI.c.obj --start-group -lgcc -lc --end-group D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtend.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtn.o
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-exit.o): in function `exit':
        exit.c:(.text.exit+0x28): undefined reference to `_exit'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-closer.o): in function `_close_r':
        closer.c:(.text._close_r+0x18): undefined reference to `_close'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
        lseekr.c:(.text._lseek_r+0x24): undefined reference to `_lseek'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-readr.o): in function `_read_r':
        readr.c:(.text._read_r+0x24): undefined reference to `_read'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-writer.o): in function `_write_r':
        writer.c:(.text._write_r+0x24): undefined reference to `_write'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
        sbrkr.c:(.text._sbrk_r+0x18): undefined reference to `_sbrk'
        collect2.exe: error: ld returned 1 exit status
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:28 (enable_language)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-c61ho8"
      binary: "D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-c61ho8"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/ELECT/CODE/vscode/ST/stm32f401ccu6/register_test/register_mode/build/CMakeFiles/CMakeScratch/TryCompile-c61ho8'
        
        Run Build Command(s): D:/APP/ninja/ninja-win/ninja.exe -v cmTC_751bb
        [1/2] D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-g++.exe   -v -o CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj -c D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-g++.exe
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --enable-mingw-wildcard --host=i686-w64-mingw32 --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_751bb.dir/'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/cc1plus.exe -quiet -v -iprefix D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/ -isysroot D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi -D__USES_INITFINI__ D:/APP/cmake/cmake-4.1.0-rc1-windows-x86_64/cmake-4.1.0-rc1-windows-x86_64/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles/cmTC_751bb.dir/ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mcpu=arm7tdmi -mfloat-abi=soft -marm -mlibarch=armv4t -march=armv4t -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc1C9ACI.s
        GNU C++17 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) version 14.2.1 20241119 (arm-none-eabi)
        	compiled by GNU C version 9.3-win32 20200320, GMP version 6.2.1, MPFR version 3.1.6, MPC version 1.0.3, isl version isl-0.15-1-g835ea3a-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include"
        ignoring nonexistent directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/lib/gcc/arm-none-eabi/14.2.1/../../../../include"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/include-fixed"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/lib/gcc/../../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include"
        ignoring duplicate directory "D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/arm-none-eabi
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include/c++/14.2.1/backward
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/include-fixed
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/include
        End of search list.
        Compiler executable checksum: dad18755456eb98fd3f9411f1449dfa2
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_751bb.dir/'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/as.exe -v -march=armv4t -mfloat-abi=soft -meabi=5 -o CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc1C9ACI.s
        GNU assembler version 2.43.1 (arm-none-eabi) using BFD version (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 2.43.1.20241119
        COMPILER_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj' '-c' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.'
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-g++.exe  -v CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_751bb   && cd ."
        FAILED: [code=1] cmTC_751bb 
        C:\\Windows\\system32\\cmd.exe /C "cd . && D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-g++.exe  -v CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_751bb   && cd ."
        Using built-in specs.
        COLLECT_GCC=D:\\APP\\arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi\\bin\\arm-none-eabi-g++.exe
        COLLECT_LTO_WRAPPER=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/lto-wrapper.exe
        Target: arm-none-eabi
        Configured with: /data/jenkins/workspace/GNU-toolchain/arm-14/src/gcc/configure --target=arm-none-eabi --prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install --with-gmp=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpfr=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-mpc=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-isl=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --disable-shared --disable-nls --disable-threads --disable-tls --enable-checking=release --enable-languages=c,c++,fortran --with-newlib --with-gnu-as --with-headers=yes --with-gnu-ld --with-native-system-header-dir=/include --with-sysroot=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/install/arm-none-eabi --with-bugurl=https://bugs.linaro.org/ --with-multilib-list=aprofile,rmprofile --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --with-libiconv-prefix=/data/jenkins/workspace/GNU-toolchain/arm-14/build-mingw-arm-none-eabi/host-tools --enable-mingw-wildcard --host=i686-w64-mingw32 --with-pkgversion='Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)'
        Thread model: single
        Supported LTO compression algorithms: zlib
        gcc version 14.2.1 20241119 (Arm GNU Toolchain 14.2.Rel1 (Build arm-14.52)) 
        COMPILER_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/
        LIBRARY_PATH=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib/;D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib/
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_751bb' '-mcpu=arm7tdmi' '-mfloat-abi=soft' '-marm' '-mlibarch=armv4t' '-march=armv4t' '-dumpdir' 'cmTC_751bb.'
         D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/collect2.exe -plugin D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/liblto_plugin.dll -plugin-opt=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../libexec/gcc/arm-none-eabi/14.2.1/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccfS9owN.res -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lc --sysroot=D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi -X -o cmTC_751bb D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crti.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtbegin.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crt0.o -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1 -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/lib -LD:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../arm-none-eabi/lib CMakeFiles/cmTC_751bb.dir/CMakeCXXCompilerABI.cpp.obj -lstdc++ -lm --start-group -lgcc -lc --end-group D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtend.o D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/crtn.o
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-exit.o): in function `exit':
        exit.c:(.text.exit+0x28): undefined reference to `_exit'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-closer.o): in function `_close_r':
        closer.c:(.text._close_r+0x18): undefined reference to `_close'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-lseekr.o): in function `_lseek_r':
        lseekr.c:(.text._lseek_r+0x24): undefined reference to `_lseek'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-readr.o): in function `_read_r':
        readr.c:(.text._read_r+0x24): undefined reference to `_read'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-writer.o): in function `_write_r':
        writer.c:(.text._write_r+0x24): undefined reference to `_write'
        D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/../../../../arm-none-eabi/bin/ld.exe: D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1\\libc.a(libc_a-sbrkr.o): in function `_sbrk_r':
        sbrkr.c:(.text._sbrk_r+0x18): undefined reference to `_sbrk'
        collect2.exe: error: ld returned 1 exit status
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
