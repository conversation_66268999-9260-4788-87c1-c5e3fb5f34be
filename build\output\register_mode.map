Archive member included to satisfy reference by file (symbol)

D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (exit)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o) (__stdio_exit_handler)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fwalk_sglue)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__sread)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (memset)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_close_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o) (errno)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_impure_ptr)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_lseek_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_read_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o) (_write_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o (__libc_init_array)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (__retarget_lock_init_recursive)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o) (_free_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_malloc_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o) (__malloc_lock)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o) (_fflush_r)
D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
                              D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o) (_sbrk_r)

Discarded input sections

 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .rodata.all_implied_fbits
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .data.__dso_handle
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text          0x00000000       0x7c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.extab     0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.exidx     0x00000000       0x10 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .debug_line_str
                0x00000000       0xd6 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .ARM.attributes
                0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text.exit     0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .debug_frame   0x00000000       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-exit.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.std      0x00000000       0x6c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.stdio_exit_handler
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.cleanup_stdio
                0x00000000       0x40 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.global_stdio_init.part.0
                0x00000000       0x40 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_acquire
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp_lock_release
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sfp    0x00000000       0xa4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__sinit  0x00000000       0x30 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .data.__sglue  0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__sf      0x00000000      0x138 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .bss.__stdio_exit_handler
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .debug_frame   0x00000000      0x144 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-findfp.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text._fwalk_sglue
                0x00000000       0x3c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .debug_frame   0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fwalk.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sread  0x00000000       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__seofread
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__swrite
                0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sseek  0x00000000       0x26 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text.__sclose
                0x00000000        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .debug_frame   0x00000000       0x88 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-stdio.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .text.memset   0x00000000       0x10 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .debug_frame   0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-memset.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text._close_r
                0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-closer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xbc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .bss.errno     0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .debug_frame   0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-reent.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_ptr
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .data._impure_data
                0x00000000       0x4c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-impure.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text._lseek_r
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lseekr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text._read_r  0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-readr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text._write_r
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-writer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_acquire_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text.__retarget_lock_release_recursive
                0x00000000        0x2 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___arc4random_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___dd_hash_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___tz_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___env_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___malloc_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___at_quick_exit_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___atexit_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .bss.__lock___sfp_recursive_mutex
                0x00000000        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .debug_frame   0x00000000       0xb0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-lock.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text._free_r  0x00000000       0x94 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .debug_frame   0x00000000       0x38 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-freer.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text.sbrk_aligned
                0x00000000       0x44 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text._malloc_r
                0x00000000      0x100 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_sbrk_start
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .bss.__malloc_free_list
                0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .debug_frame   0x00000000       0x50 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mallocr.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_lock
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text.__malloc_unlock
                0x00000000        0xc D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .debug_frame   0x00000000       0x30 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-mlock.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.__sflush_r
                0x00000000      0x104 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text._fflush_r
                0x00000000       0x50 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text.fflush   0x00000000       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .debug_frame   0x00000000       0x5c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-fflush.o)
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .text._sbrk_r  0x00000000       0x20 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .ARM.attributes
                0x00000000       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-sbrkr.o)
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .text          0x00000000        0x0 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .data          0x00000000        0x0 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .bss           0x00000000        0x0 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .text          0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .data          0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .bss           0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .data.GPIOA    0x00000000        0x4 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .data.RCC      0x00000000        0x4 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0xad2 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x2a CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x9e CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x364 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x112 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x4a CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x34 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x10 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x58 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x1e5 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x170 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x16 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x3c CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .group         0x00000000        0xc CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .text          0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .data          0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .bss           0x00000000        0x0 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0xad2 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x22 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x8e CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x51 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0x103 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000       0x6a CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_macro   0x00000000      0x1df CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .text          0x00000000       0x14 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
 .data          0x00000000        0x0 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
 .bss           0x00000000        0x0 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .rodata.all_implied_fbits
                0x00000000       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .eh_frame      0x00000000        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .text          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 .data          0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 .bss           0x00000000        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
RAM              0x20000000         0x00010000         xrw
FLASH            0x08000000         0x00040000         xr
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crt0.o
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libm.a
END GROUP
LOAD CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
LOAD CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
LOAD CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
LOAD CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libg_nano.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
END GROUP
START GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a
END GROUP
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
                0x20010000                        _estack = (ORIGIN (RAM) + LENGTH (RAM))
                0x00000200                        _Min_Heap_Size = 0x200
                0x00000400                        _Min_Stack_Size = 0x400

.isr_vector     0x08000000      0x194
                0x08000000                        . = ALIGN (0x4)
 *(.isr_vector)
 .isr_vector    0x08000000      0x194 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
                0x08000000                g_pfnVectors
                0x08000194                        . = ALIGN (0x4)

.text           0x08000194      0x2d8
                0x08000194                        . = ALIGN (0x4)
 *(.text)
 *(.text*)
 .text.deregister_tm_clones
                0x08000194       0x1c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.register_tm_clones
                0x080001b0       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.__do_global_dtors_aux
                0x080001d4       0x28 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.frame_dummy
                0x080001fc       0x24 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .text.__libc_init_array
                0x08000220       0x48 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
                0x08000220                __libc_init_array
 .text.GPIOA_Init
                0x08000268       0x98 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                0x08000268                GPIOA_Init
 .text.LED_OFF  0x08000300       0x34 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                0x08000300                LED_OFF
 .text.LED_ON   0x08000334       0x34 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                0x08000334                LED_ON
 .text.main     0x08000368       0x88 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
                0x08000368                main
 .text.SystemInit
                0x080003f0        0xe CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
                0x080003f0                SystemInit
 *fill*         0x080003fe        0x2 
 .text.Reset_Handler
                0x08000400       0x50 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
                0x08000400                Reset_Handler
 .text.Default_Handler
                0x08000450        0x2 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
                0x08000450                RTC_Alarm_IRQHandler
                0x08000450                EXTI2_IRQHandler
                0x08000450                DebugMon_Handler
                0x08000450                SPI4_IRQHandler
                0x08000450                TIM1_CC_IRQHandler
                0x08000450                DMA2_Stream5_IRQHandler
                0x08000450                HardFault_Handler
                0x08000450                DMA1_Stream5_IRQHandler
                0x08000450                SysTick_Handler
                0x08000450                PVD_IRQHandler
                0x08000450                SDIO_IRQHandler
                0x08000450                TAMP_STAMP_IRQHandler
                0x08000450                PendSV_Handler
                0x08000450                NMI_Handler
                0x08000450                EXTI3_IRQHandler
                0x08000450                TIM1_UP_TIM10_IRQHandler
                0x08000450                I2C3_ER_IRQHandler
                0x08000450                EXTI0_IRQHandler
                0x08000450                I2C2_EV_IRQHandler
                0x08000450                DMA1_Stream2_IRQHandler
                0x08000450                FPU_IRQHandler
                0x08000450                UsageFault_Handler
                0x08000450                DMA2_Stream2_IRQHandler
                0x08000450                SPI1_IRQHandler
                0x08000450                TIM1_BRK_TIM9_IRQHandler
                0x08000450                DMA2_Stream3_IRQHandler
                0x08000450                USART6_IRQHandler
                0x08000450                DMA2_Stream0_IRQHandler
                0x08000450                TIM4_IRQHandler
                0x08000450                I2C1_EV_IRQHandler
                0x08000450                DMA1_Stream6_IRQHandler
                0x08000450                DMA1_Stream1_IRQHandler
                0x08000450                TIM3_IRQHandler
                0x08000450                RCC_IRQHandler
                0x08000450                Default_Handler
                0x08000450                EXTI15_10_IRQHandler
                0x08000450                ADC_IRQHandler
                0x08000450                DMA1_Stream7_IRQHandler
                0x08000450                TIM5_IRQHandler
                0x08000450                DMA2_Stream7_IRQHandler
                0x08000450                I2C3_EV_IRQHandler
                0x08000450                EXTI9_5_IRQHandler
                0x08000450                RTC_WKUP_IRQHandler
                0x08000450                SPI2_IRQHandler
                0x08000450                MemManage_Handler
                0x08000450                DMA1_Stream0_IRQHandler
                0x08000450                SVC_Handler
                0x08000450                EXTI4_IRQHandler
                0x08000450                WWDG_IRQHandler
                0x08000450                TIM2_IRQHandler
                0x08000450                OTG_FS_WKUP_IRQHandler
                0x08000450                TIM1_TRG_COM_TIM11_IRQHandler
                0x08000450                EXTI1_IRQHandler
                0x08000450                USART2_IRQHandler
                0x08000450                I2C2_ER_IRQHandler
                0x08000450                DMA2_Stream1_IRQHandler
                0x08000450                FLASH_IRQHandler
                0x08000450                DMA2_Stream4_IRQHandler
                0x08000450                BusFault_Handler
                0x08000450                USART1_IRQHandler
                0x08000450                OTG_FS_IRQHandler
                0x08000450                SPI3_IRQHandler
                0x08000450                DMA1_Stream4_IRQHandler
                0x08000450                I2C1_ER_IRQHandler
                0x08000450                DMA2_Stream6_IRQHandler
                0x08000450                DMA1_Stream3_IRQHandler
 *(.glue_7)
 .glue_7        0x08000452        0x0 linker stubs
 *(.glue_7t)
 .glue_7t       0x08000452        0x0 linker stubs
 *(.eh_frame)
 *fill*         0x08000452        0x2 
 .eh_frame      0x08000454        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 *(.init)
 .init          0x08000454        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
                0x08000454                _init
 .init          0x08000458        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
 *(.fini)
 .fini          0x08000460        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
                0x08000460                _fini
 .fini          0x08000464        0x8 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
                0x0800046c                        . = ALIGN (0x4)
                0x0800046c                        _etext = .

.vfp11_veneer   0x0800046c        0x0
 .vfp11_veneer  0x0800046c        0x0 linker stubs

.v4_bx          0x0800046c        0x0
 .v4_bx         0x0800046c        0x0 linker stubs

.iplt           0x0800046c        0x0
 .iplt          0x0800046c        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o

.rel.dyn        0x0800046c        0x0
 .rel.iplt      0x0800046c        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o

.rodata         0x0800046c        0x0
                0x0800046c                        . = ALIGN (0x4)
 *(.rodata)
 *(.rodata*)
                0x0800046c                        . = ALIGN (0x4)

.ARM.extab
 *(.ARM.extab* .gnu.linkonce.armextab.*)

.ARM            0x0800046c        0x0
                0x0800046c                        __exidx_start = .
 *(.ARM.exidx*)
                0x0800046c                        __exidx_end = .

.preinit_array  0x0800046c        0x0
                0x0800046c                        PROVIDE (__preinit_array_start = .)
 *(.preinit_array*)
                0x0800046c                        PROVIDE (__preinit_array_end = .)

.init_array     0x0800046c        0x4
                0x0800046c                        PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array*)
 .init_array    0x0800046c        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                0x08000470                        PROVIDE (__init_array_end = .)

.fini_array     0x08000470        0x4
                [!provide]                        PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array*)
 .fini_array    0x08000470        0x4 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                [!provide]                        PROVIDE (__fini_array_end = .)
                0x08000474                        _sidata = LOADADDR (.data)

.data           0x20000000        0x8 load address 0x08000474
                0x20000000                        . = ALIGN (0x4)
                0x20000000                        _sdata = .
 *(.data)
 *(.data*)
 .data.GPIOA    0x20000000        0x4 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                0x20000000                GPIOA
 .data.RCC      0x20000004        0x4 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                0x20000004                RCC
                0x20000008                        . = ALIGN (0x4)
                0x20000008                        _edata = .

.tm_clone_table
                0x20000008        0x0 load address 0x0800047c
 .tm_clone_table
                0x20000008        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .tm_clone_table
                0x20000008        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o

.igot.plt       0x20000008        0x0 load address 0x0800047c
 .igot.plt      0x20000008        0x0 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
                0x20000008                        . = ALIGN (0x4)

.bss            0x20000008       0x20 load address 0x0800047c
                0x20000008                        _sbss = .
                0x20000008                        __bss_start__ = _sbss
 *(.bss)
 *(.bss*)
 .bss.completed.1
                0x20000008        0x1 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 *fill*         0x20000009        0x3 
 .bss.object.0  0x2000000c       0x18 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .bss.led_case  0x20000024        0x1 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
                0x20000024                led_case
 *(COMMON)
                0x20000028                        . = ALIGN (0x4)
 *fill*         0x20000025        0x3 
                0x20000028                        _ebss = .
                0x20000028                        __bss_end__ = _ebss

._user_heap_stack
                0x20000028      0x600 load address 0x0800047c
                0x20000028                        . = ALIGN (0x8)
                [!provide]                        PROVIDE (end = .)
                [!provide]                        PROVIDE (_end = .)
                0x20000228                        . = (. + _Min_Heap_Size)
 *fill*         0x20000028      0x200 
                0x20000628                        . = (. + _Min_Stack_Size)
 *fill*         0x20000228      0x400 
                0x20000628                        . = ALIGN (0x8)

/DISCARD/
 libc.a(*)
 libm.a(*)
 libgcc.a(*)

.ARM.attributes
                0x00000000       0x30
 *(.ARM.attributes)
 .ARM.attributes
                0x00000000       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crti.o
 .ARM.attributes
                0x00000022       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtbegin.o
 .ARM.attributes
                0x00000056       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .ARM.attributes
                0x0000008a       0x34 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .ARM.attributes
                0x000000be       0x34 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .ARM.attributes
                0x000000f2       0x34 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .ARM.attributes
                0x00000126       0x21 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
 .ARM.attributes
                0x00000147       0x34 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtend.o
 .ARM.attributes
                0x0000017b       0x22 D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard/crtn.o
OUTPUT(output\register_mode.elf elf32-littlearm)
LOAD linker stubs
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libm.a
LOAD D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libgcc.a

.debug_frame    0x00000000      0x108
 .debug_frame   0x00000000       0x2c D:/APP/arm-gnu-toolchain-14.2.rel1-mingw-w64-i686-arm-none-eabi/bin/../lib/gcc/arm-none-eabi/14.2.1/thumb/v7e-m+fp/hard\libc_nano.a(libc_a-init.o)
 .debug_frame   0x0000002c       0x80 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_frame   0x000000ac       0x2c CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_frame   0x000000d8       0x30 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_info     0x00000000      0x747
 .debug_info    0x00000000      0x34e CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_info    0x0000034e      0x346 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_info    0x00000694       0x83 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_info    0x00000717       0x30 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.debug_abbrev   0x00000000      0x273
 .debug_abbrev  0x00000000      0x103 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_abbrev  0x00000103      0x107 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_abbrev  0x0000020a       0x45 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_abbrev  0x0000024f       0x24 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.debug_aranges  0x00000000       0x98
 .debug_aranges
                0x00000000       0x30 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_aranges
                0x00000030       0x20 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_aranges
                0x00000050       0x20 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_aranges
                0x00000070       0x28 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.debug_rnglists
                0x00000000       0x60
 .debug_rnglists
                0x00000000       0x20 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_rnglists
                0x00000020       0x14 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_rnglists
                0x00000034       0x13 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_rnglists
                0x00000047       0x19 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.debug_macro    0x00000000     0x1f38
 .debug_macro   0x00000000      0x189 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000189      0xad2 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000c5b       0x22 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000c7d       0x8e CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000d0b       0x51 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000d5c      0x103 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000e5f       0x6a CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00000ec9      0x1df CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000010a8       0x7b CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001123       0x2a CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x0000114d       0x3c CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001189       0x34 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000011bd       0x16 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000011d3       0x9e CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001271      0x364 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000015d5      0x112 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000016e7       0x10 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000016f7       0x16 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x0000170d       0x4a CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001757       0x34 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x0000178b       0x10 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x0000179b       0x58 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000017f3      0x1e5 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000019d8       0x16 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x000019ee       0x16 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001a04      0x170 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001b74       0x16 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001b8a       0x3c CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001bc6       0x22 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_macro   0x00001be8      0x183 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001d6b       0x75 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001de0       0x94 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001e74       0x57 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_macro   0x00001ecb       0x6d CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_line     0x00000000      0xbd2
 .debug_line    0x00000000      0x46c CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
 .debug_line    0x0000046c      0x421 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_line    0x0000088d      0x2cb CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_line    0x00000b58       0x7a CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.debug_str      0x00000000     0x7498
 .debug_str     0x00000000     0x7498 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                               0x7542 (size before relaxing)
 .debug_str     0x00007498     0x754f CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .debug_str     0x00007498     0x3dd1 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj
 .debug_str     0x00007498       0xbc CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj

.comment        0x00000000       0x45
 .comment       0x00000000       0x45 CMakeFiles/register_mode.dir/code/App/Src/appled.c.obj
                                 0x46 (size before relaxing)
 .comment       0x00000045       0x46 CMakeFiles/register_mode.dir/code/User/Src/main.c.obj
 .comment       0x00000045       0x46 CMakeFiles/register_mode.dir/code/User/Src/system_stm32f4xx.c.obj

.debug_line_str
                0x00000000       0xa0
 .debug_line_str
                0x00000000       0xa0 CMakeFiles/register_mode.dir/startup_stm32f401xc.s.obj
